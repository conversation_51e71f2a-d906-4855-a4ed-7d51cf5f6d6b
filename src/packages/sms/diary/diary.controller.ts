import { Body, Controller, Get, Param, Post, Query } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { CreateDiaryDto, ListDiariesQueryDto } from "./dto/diary.dto.js";
import { DiaryService } from "./diary.service.js";
import { SectionIdParamDto } from "./dto/section-param.dto.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";

@Controller("sms/sections")
@ApiTags("Diary")
export class DiaryController {
  public constructor(private readonly diaryService: DiaryService) {}

  /**
   * Create a new diary entry for a specific class section
   */
  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN", "TEACHER"])
  @Post("/:sectionId/diary")
  public create(
    @Body() createDiaryDto: CreateDiaryDto,
    @Param() param: SectionIdParamDto,
  ) {
    return this.diaryService.create({
      ...createDiaryDto,
      classSectionId: param.sectionId,
    });
  }

  /**
   * Get all diary entries for a specific class section
   * Supports filtering by date, month, date range, and subject
   */
  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN", "TEACHER"])
  @Get("/:sectionId/diary")
  public getAllBySectionId(
    @Param() param: SectionIdParamDto,
    @Query() query: ListDiariesQueryDto,
  ) {
    return this.diaryService.findAllBySectionId(param.sectionId, query);
  }
}
