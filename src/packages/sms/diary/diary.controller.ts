import { Body, Controller, Get, Param, Post } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { CreateDiaryDto } from "./dto/diary.dto.js";
import { DiaryService } from "./diary.service.js";
import { SectionIdParamDto } from "./dto/section-param.dto.js";

@Controller("sms/sections")
@ApiTags("Diary")
export class DiaryController {
  public constructor(private readonly diaryService: DiaryService) {}


  @Post("/:sectionId/diary")
  public create(
    @Body() createDiaryDto: CreateDiaryDto,
    @Param() param: SectionIdParamDto,
  ) {
    return this.diaryService.create({
      ...createDiaryDto,
      classSectionId: param.sectionId,
    });
  }
}
