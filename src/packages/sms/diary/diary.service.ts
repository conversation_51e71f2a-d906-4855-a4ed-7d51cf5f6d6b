import { Injectable, Logger } from "@nestjs/common";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { Kysely } from "kysely";
import { DB } from "../../../database/types.js";
import { CreateDiaryDto } from "./dto/diary.dto.js";
import { NewDiary } from "./types/diary.type.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";
import { ClassSectionsService } from "../classes/sections/class-sections.service.js";
import { SubjectsService } from "../subjects/subjects.service.js";
import { EntityId } from "../../../shared/types/shared.types.js";

@Injectable()
export class DiaryService {
  private readonly logger = new Logger(DiaryService.name);

  constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly classSectionsService: ClassSectionsService,
    private readonly subjectsService: SubjectsService,
  ) {}

  public async create(newDiaryData: NewDiary): Promise<EntityId> {
    try {
      await this.classSectionsService.verifyClassSectionExists(
        newDiaryData.classSectionId,
      );

      await this.subjectsService.verifySubjectExists(newDiaryData.subjectId);

      return await this.db
        .insertInto("diary")
        .values(newDiaryData)
        .returning(["id"])
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to create diary", error);
      handleDatabaseInsertException(error, {
        resource: "diary",
        logger: this.logger,
      });
    }
  }
}
