import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import {
  getUuidSchema,
  dateSchema,
} from "../../../../shared/schema/zod-common.schema.js";

const diaryBaseSchema = z.object({
  subjectId: getUuidSchema("Subject ID"),
  content: z.string().nonempty(),
  date: dateSchema,
});

// ------------------- Create-Diary-Dto ------------------->
export const createDiarySchema = diaryBaseSchema;
export class CreateDiaryDto extends createZodDto(diaryBaseSchema) {}
