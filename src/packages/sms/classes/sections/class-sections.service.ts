import {
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from "@nestjs/common";
import { InjectKysely } from "../../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { Kysely, sql } from "kysely";
import { DB } from "../../../../database/types.js";
import {
  ClassSection,
  ClassSectionResponse,
  ClassSectionUpdate,
  NewClassSection,
} from "./types/class-sections.types.js";
import { handleDatabaseInsertException } from "../../../../utils/pg-utils.js";
import { ClassesService } from "../classes.service.js";
import { Class } from "../types/classes.types.js";
import { ModuleRef } from "@nestjs/core";
import { StaffService } from "../../staff/staff.service.js";
import {
  EntityIdResponse,
  ServiceOptions,
} from "../../../../shared/types/common.types.js";

@Injectable()
export class ClassSectionsService implements OnModuleInit {
  private readonly logger = new Logger(ClassSectionsService.name);

  private classesService: ClassesService;
  private staffService: StaffService;

  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly moduleRef: ModuleRef,
  ) {}

  onModuleInit() {
    this.classesService = this.moduleRef.get(ClassesService, {
      strict: false,
    });
    this.staffService = this.moduleRef.get(StaffService, {
      strict: false,
    });
  }

  public async create(
    createSectionPayload: NewClassSection,
    options?: ServiceOptions,
  ): Promise<EntityIdResponse> {
    try {
      await this.classesService.verifyClassExists(createSectionPayload.classId);

      const kyselyClient = options?.trx ?? this.db;
      return await kyselyClient
        .insertInto("classSection")
        .values(createSectionPayload)
        .returning(["id"])
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to create section", error);
      handleDatabaseInsertException(error, {
        resource: "classSection",
        logger: this.logger,
        messages: {
          uniqueConstraint: "Section with same name already exists",
        },
      });
    }
  }

  public async update(
    id: ClassSection["id"],
    updateSectionPayload: ClassSectionUpdate,
    options?: ServiceOptions,
  ): Promise<EntityIdResponse> {
    try {
      if (updateSectionPayload.classTeacherId) {
        await this.staffService.verifyClassTeacherExists(
          updateSectionPayload.classTeacherId,
          options,
        );
      }
      const kyselyClient = options?.trx ?? this.db;
      return await kyselyClient
        .updateTable("classSection")
        .set(updateSectionPayload)
        .where("id", "=", id)
        .returning(["id"])
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to update section", error);
      throw error;
    }
  }

  public async createOrUpdate(
    payload: NewClassSection,
    options?: ServiceOptions,
  ): Promise<EntityIdResponse> {
    try {
      const existingSection = await this.findClassSection(
        {
          id: payload.id,
          classId: payload.classId,
        },
        options,
      );

      if (existingSection) {
        return await this.update(existingSection.id, payload, options);
      }
      return await this.create(payload, options);
    } catch (error: unknown) {
      this.logger.error("Failed to create or update section", error);
      handleDatabaseInsertException(error, {
        resource: "classSection",
        logger: this.logger,
      });
    }
  }
  /*
  |--------------------------------------------------------------------------
  | Create Many Sections
  |--------------------------------------------------------------------------
  |
  | This method is used to create multiple class sections in a single operation.
  | It is designed to be called internally from other services and not directly
  | from controllers. The method accepts an array of section data and inserts
  | them into the database, returning the created sections with their IDs.
  |
  */
  public async createMany(
    createManySectionPayload: NewClassSection[],
    options?: ServiceOptions,
  ): Promise<void> {
    for (const section of createManySectionPayload) {
      await this.staffService.verifyClassTeacherExists(
        section.classTeacherId,
        options,
      );
    }

    try {
      const kyselyClient = options?.trx ?? this.db;
      await kyselyClient
        .insertInto("classSection")
        .values(createManySectionPayload)
        .execute();
    } catch (error: unknown) {
      this.logger.error("Failed to create sections", error);
      handleDatabaseInsertException(error, {
        resource: "classSection",
        logger: this.logger,
      });
    }
  }

  public async findById(id: ClassSection["id"], options?: ServiceOptions) {
    try {
      const kyselyClient = options?.trx ?? this.db;
      return await kyselyClient
        .selectFrom("classSection")
        .selectAll()
        .where("id", "=", id)
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find section by id", error);
      throw error;
    }
  }

  public async find(
    criteria: Partial<ClassSection> & Pick<Class, "academicSessionId">,
  ) {
    try {
      let query = this.db
        .selectFrom("classSection")
        .innerJoin("class", "class.id", "classSection.classId")
        .select([
          "classSection.id",
          "classSection.name",
          sql<{ id: string; name: string }>`json_build_object(
            'id', class.id,
            'name', class.name
          )`.as("class"),
        ]);

      if (criteria.id) {
        query = query.where("classSection.id", "=", criteria.id);
      }

      if (criteria.name) {
        query = query.where("classSection.name", "=", criteria.name);
      }

      if (criteria.classId) {
        query = query.where("classSection.classId", "=", criteria.classId);
      }

      if (criteria.classTeacherId) {
        query = query.where(
          "classSection.classTeacherId",
          "=",
          criteria.classTeacherId,
        );
      }

      if (criteria.isActive) {
        query = query.where("classSection.isActive", "=", criteria.isActive);
      }

      if (criteria.createdAt) {
        query = query.where("classSection.createdAt", "=", criteria.createdAt);
      }

      if (criteria.academicSessionId) {
        query = query.where(
          "class.academicSessionId",
          "=",
          criteria.academicSessionId,
        );
      }
      return await query.execute();
    } catch (error: unknown) {
      this.logger.error("Failed to find section", error);
      throw error;
    }
  }

  public async getAllByClassId(classId: Class["id"]) {
    try {
      return await this.db
        .selectFrom("classSection")
        .selectAll()
        .where("classId", "=", classId)
        .execute();
    } catch (error: unknown) {
      this.logger.error("Failed to get all sections", error);
      throw error;
    }
  }

  public async verifyClassSectionExists(
    classSectionId: string,
    options?: ServiceOptions,
  ) {
    let existingClassSection: ClassSection | undefined;
    try {
      existingClassSection = await this.findById(classSectionId, options);
    } catch (error: unknown) {
      this.logger.error("Failed to verify class section exists", error);
      throw error;
    }

    if (!existingClassSection) {
      throw new NotFoundException(
        `Class section with id: ${classSectionId} not found`,
      );
    }
  }

  private async findClassSection(
    criteria: Partial<ClassSection>,
    options?: ServiceOptions,
  ) {
    const kyselyClient = options?.trx ?? this.db;
    let query = kyselyClient.selectFrom("classSection");

    if (criteria.id) {
      query = query.where("id", "=", criteria.id);
    }

    if (criteria.name) {
      query = query.where("name", "=", criteria.name);
    }

    if (criteria.classId) {
      query = query.where("classId", "=", criteria.classId);
    }

    if (criteria.classTeacherId) {
      query = query.where("classTeacherId", "=", criteria.classTeacherId);
    }

    if (criteria.isActive) {
      query = query.where("isActive", "=", criteria.isActive);
    }

    if (criteria.createdAt) {
      query = query.where("createdAt", "=", criteria.createdAt);
    }

    return query.selectAll().executeTakeFirst();
  }
}
