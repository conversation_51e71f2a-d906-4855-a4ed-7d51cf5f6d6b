import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from "@nestjs/common";
import { ApiTags, ApiExtraModels } from "@nestjs/swagger";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { SubjectsService } from "./subjects.service.js";
import {
  CreateSubjectDto,
  UpdateSubjectDto,
  SubjectResponseDto,
} from "./dto/subjects.dto.js";
import { ListAllEntitiesQueryDto } from "../../../shared/dto/query.dto.js";
import { AcademicSessionIdParamDto } from "../../../shared/dto/param.dto.js";
import { SessionSubjectParamDto } from "./dto/subjects-param.dto.js";
import {
  ApiDocGetAllSubjects,
  ApiDocCreateSubject,
  ApiDocUpdateSubject,
  ApiDocGetSubjectById,
} from "./docs/subjects.docs.js";

@ApiTags("Subjects")
@ApiExtraModels(SubjectResponseDto)
@Controller("sms/sessions")
export class SubjectsController {
  public constructor(private readonly subjectService: SubjectsService) {}

  /**
   * Get all subjects for a specific academic session
   */
  @Roles(["INSTITUTE_OWNER"])
  @Get(":sessionId/subjects")
  @ApiDocGetAllSubjects()
  public async getAll(
    @Param() param: AcademicSessionIdParamDto,
    @Query() query: ListAllEntitiesQueryDto,
  ) {
    return this.subjectService.findAll(param.sessionId, query);
  }

  /**
   * Create a new subject for a specific academic session
   */
  @Roles(["INSTITUTE_OWNER"])
  @Post(":sessionId/subjects")
  @ApiDocCreateSubject()
  public create(
    @Body() createSubjectDto: CreateSubjectDto,
    @Param() param: AcademicSessionIdParamDto,
  ) {
    return this.subjectService.create({
      ...createSubjectDto,
      academicSessionId: param.sessionId,
    });
  }

  /**
   * Update an existing subject
   */
  @Roles(["INSTITUTE_OWNER"])
  @Patch(":sessionId/subjects/:subjectId")
  @ApiDocUpdateSubject()
  public async update(
    @Param() param: SessionSubjectParamDto,
    @Body() updateSubjectDto: UpdateSubjectDto,
  ) {
    return await this.subjectService.update(param.subjectId, updateSubjectDto);
  }

  /**
   * Get a specific subject by ID
   */
  @Roles(["INSTITUTE_OWNER"])
  @Get(":sessionId/subjects/:subjectId")
  @ApiDocGetSubjectById()
  public async getById(@Param() param: SessionSubjectParamDto) {
    return await this.subjectService.findSubjectOrThrow(param.subjectId);
  }
}
