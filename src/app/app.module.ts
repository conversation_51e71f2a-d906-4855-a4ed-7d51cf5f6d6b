import { Module } from "@nestjs/common";
import { AppService } from "./app.service.js";
import { AppController } from "./app.controller.js";
import { ConfigModule } from "@nestjs/config";
import { AuthModule } from "../core/auth/auth.module.js";
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from "@nestjs/core";
import { CustomZodValidationPipe } from "../lib/zod.js";
import { ZodSerializerInterceptor } from "nestjs-zod";
import { AllExceptionFilter } from "../shared/filters/All-exception.filter.js";
import { envSchema } from "../shared/services/env/env.schema.js";
import { CamelCasePlugin, PostgresDialect } from "kysely";
import pg from "pg";
import { KyselyModule } from "../shared/modules/kysely/kysely.module.js";
import { readFileSync } from "fs";
import { ServeStaticModule } from "@nestjs/serve-static";
import { join } from "path";
import { InstitutesModule } from "../packages/sms/institutes/institutes.module.js";
import { MailerModule } from "@nestjs-modules/mailer";
import { FileUploadModule } from "../shared/modules/file-uploads/fileuploads.module.js";
import { JwtAuthGuard } from "../shared/guards/jwt-auth.guard.js";
import { RolesGuard } from "../core/roles/guards/roles.guard.js";
import { jwtConstants } from "../core/auth/constants/jwt-constants.js";
import { JwtModule } from "@nestjs/jwt";
import { SubscriptionPlansModule } from "../packages/platform/subscriptions-plans/subscription-plans.module.js";
import { InstituteOwnersModule } from "../packages/sms/institute-owners/owners.module.js";
import { BranchesModule } from "../packages/sms/branches/branches.module.js";
import { AcademicSessionsModule } from "../packages/sms/academic-sessions/academic-sessions.module.js";
import { EnvService } from "../shared/services/env/env.service.js";
import { CommonModule } from "../shared/shared.module.js";
import { StaffModule } from "../packages/sms/staff/staff.module.js";
import { ClassesModule } from "../packages/sms/classes/classes.module.js";
import { UsersModule } from "../core/users/users.module.js";
import { ClassSectionsModule } from "../packages/sms/classes/sections/class-sections.module.js";
import { StudentsModule } from "../packages/sms/students/students.module.js";
import { SubjectsModule } from "../packages/sms/subjects/subjects.module.js";
import { SectionSubjectsModule } from "../packages/sms/sections-subject-assignment/section-subjects.module.js";
import { DiaryModule } from "../packages/sms/diary/diary.module.js";
import { DrizzleModule } from "../shared/modules/drizzle/drizzle.module.js";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ".env",
      validate: env => envSchema.parse(env),
    }),
    JwtModule.register({
      global: true,
      secret: jwtConstants.accessToken.secret,
      signOptions: { expiresIn: jwtConstants.accessToken.expireTimeInSec },
    }),
    ServeStaticModule.forRootAsync({
      useFactory: (envService: EnvService) => {
        return [
          {
            rootPath: join(
              import.meta.dirname,
              "..",
              "..",
              envService.get("UPLOADS_DIR"),
            ),
            serveRoot: `${envService.get("API_PREFIX")}/${envService.get("UPLOADS_DIR")}`,
          },
        ];
      },
      inject: [EnvService],
    }),
    KyselyModule.forRootAsync({
      inject: [EnvService],
      useFactory: (envService: EnvService) => ({
        dialect: new PostgresDialect({
          pool: new pg.Pool({
            database: envService.get("DATABASE_NAME"),
            host: envService.get("DATABASE_HOST"),
            port: envService.get("DATABASE_PORT"),
            user: envService.get("DATABASE_USER"),
            password:
              envService.get("DATABASE_PASSWORD") ??
              readFileSync(envService.get("DATABASE_PASSWORD_FILE"), "utf-8"),
          }),
        }),
        plugins: [new CamelCasePlugin()],
      }),
    }),
    DrizzleModule.forRootAsync({
      useFactory: (envService: EnvService) => ({
        postgres: {
          url: envService.get("DATABASE_URL"),
        },
      }),
      inject: [EnvService],
    }),
    MailerModule.forRootAsync({
      useFactory: (envService: EnvService) => ({
        transport: {
          host: envService.get("EMAIL_HOST"),
          port: envService.get("EMAIL_PORT"),
          auth: {
            user: envService.get("EMAIL_USERNAME"),
            pass: envService.get("EMAIL_PASSWORD"),
          },
        },
      }),
      inject: [EnvService],
    }),
    CommonModule,
    UsersModule,
    SubscriptionPlansModule,
    AuthModule,
    FileUploadModule,
    InstituteOwnersModule,
    InstitutesModule,
    BranchesModule,
    AcademicSessionsModule,
    StaffModule,
    ClassesModule,
    StudentsModule,
    ClassSectionsModule,
    SubjectsModule,
    SectionSubjectsModule,
    DiaryModule,
  ],
  controllers: [AppController],
  exports: [AppService],
  providers: [
    AppService,
    // validates req body using zod schema
    {
      provide: APP_PIPE,
      useClass: CustomZodValidationPipe,
    },
    // intercepts and validates api response using zod schema dto
    {
      provide: APP_INTERCEPTOR,
      useClass: ZodSerializerInterceptor,
    },
    // extended filter to handle zod serialization exceptions and http exceptions
    {
      provide: APP_FILTER,
      useClass: AllExceptionFilter,
    },
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
  ],
})
export class AppModule {}
