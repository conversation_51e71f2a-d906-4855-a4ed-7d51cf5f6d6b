import { z } from "zod";
import { listAllEntitiesQuerySchema } from "../schema/zod-common.schema.js";
import { DB } from "../../database/types.js";
import { Transaction } from "kysely";

export type ListAllEntitiesQueryOptions = z.infer<
  typeof listAllEntitiesQuerySchema
>;

export interface ServiceOptions {
  trx?: Transaction<DB>;
}

export interface FindAllEntitiesResponse<T> {
  items: T[];
  total: number | string | bigint;
}

export interface EntityId {
  id: string;
}
