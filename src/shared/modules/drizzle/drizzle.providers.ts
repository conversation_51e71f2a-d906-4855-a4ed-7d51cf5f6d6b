import { Provider } from "@nestjs/common";
import { DrizzleService } from "./drizzle.service.js";
import { DRIZZLE_MODULE_CONNECTION_TOKEN } from "./drizzle.constants.js";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { DrizzlePostgresConfig } from "./drizzle.interface.js";

export function createDrizzleClient(
  config: DrizzlePostgresConfig,
  service: DrizzleService,
) {
  const queryClient = postgres(config.postgres.url);

  const db = drizzle({
    client: queryClient,
    casing: "snake_case",
    ...config.config,
  }); 
  service.addClient(db);
  return db;
}

export function createDrizzleProviders(
  config: DrizzlePostgresConfig,
): Provider[] {
  return [
    {
      inject: [DrizzleService],
      provide: DRIZZLE_MODULE_CONNECTION_TOKEN,
      useFactory: (drizzleService: DrizzleService) => {
        return createDrizzleClient(config, drizzleService);
      },
    },
  ];
}
