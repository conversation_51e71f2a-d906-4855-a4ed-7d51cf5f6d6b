import {
  pgTable,
  uuid,
  text,
  date,
  numeric,
  timestamp,
  unique,
  pgEnum,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { usersTable, genderEnum } from "./auth.schema.js";
import { academicSessionTable } from "./academic.schema.js";

// ==================== ENUMS ====================

/**
 * Guardian relation enumeration
 */
export const guardianRelationEnum = pgEnum("guardianRelation", [
  "FATHER",
  "MOTHER",
  "GUARDIAN",
]);

/**
 * Religion enumeration
 */
export const religionEnum = pgEnum("religion", [
  "ISLAM",
  "CHRISTIANITY",
  "HINDUISM",
  "BUDDHISM",
  "SIKHISM",
  "JUDAISM",
  "OTHER",
]);

/**
 * Enrollment type enumeration
 */
export const enrollmentTypeEnum = pgEnum("enrollmentType", [
  "ADMISSION",
  "TRANSFER_IN",
  "REPEATING",
  "PROMOTION",
]);

/**
 * Enrollment status enumeration
 */
export const enrollmentStatusEnum = pgEnum("enrollmentStatus", [
  "ACTIVE",
  "EXPELLED",
  "GRADUATED",
  "DECEASED",
  "COMPLETED",
  "WITHDRAWN",
]);

// ==================== TABLES ====================

/**
 * Guardian table
 * Links users to guardian role with relationship information
 */
export const guardianTable = pgTable("guardian", {
  userId: uuid("userId")
    .primaryKey()
    .references(() => usersTable.id, {
      onDelete: "restrict",
      onUpdate: "cascade",
    }),
  relation: guardianRelationEnum("relation").notNull(),
  createdAt: timestamp("createdAt", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});

/**
 * Student table
 * Student profiles and information
 * Note: classSectionId will be referenced from education.schema.ts
 */
export const studentTable = pgTable("student", {
  id: uuid("id")
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  name: text("name").notNull(),
  registrationNumber: text("registrationNumber"),
  email: text("email").unique(),
  fatherName: text("fatherName").notNull(),
  address: text("address").notNull(),
  gender: genderEnum("gender").notNull(),
  photo: text("photo"),
  religion: religionEnum("religion").notNull(),
  monthlyFee: numeric("monthlyFee", { precision: 10, scale: 2 }).notNull(),
  admissionDate: date("admissionDate").notNull(),
  dateOfBirth: date("dateOfBirth").notNull(),
  previousSchool: text("previousSchool"),
  classSectionId: uuid("classSectionId").notNull(),
  // Note: Foreign key reference will be added in education.schema.ts
  guardianId: uuid("guardianId")
    .notNull()
    .references(() => guardianTable.userId, {
      onDelete: "restrict",
      onUpdate: "cascade",
    }),
  createdAt: timestamp("createdAt", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});

/**
 * Enrollment table
 * Tracks student enrollments across academic sessions
 * Note: classSectionId and academicSessionId will be referenced from other schemas
 */
export const enrollmentTable = pgTable(
  "enrollment",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    studentId: uuid("studentId")
      .notNull()
      .references(() => studentTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    classSectionId: uuid("classSectionId").notNull(),
    // Note: Foreign key reference will be added in education.schema.ts
    academicSessionId: uuid("academicSessionId")
      .notNull()
      .references(() => academicSessionTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    type: enrollmentTypeEnum("type").notNull(),
    status: enrollmentStatusEnum("status").notNull(),
    date: date("date").notNull(),
    createdAt: timestamp("createdAt", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Unique constraint for student per academic session
    unique("unique_student_session_per_enrollment").on(
      table.studentId,
      table.academicSessionId,
    ),
    // Unique index for non-repeating students per section
    unique("idx_unique_student_per_section_if_not_repeating").on(
      table.studentId,
      table.classSectionId,
      table.type,
    ),
    // Unique index for active status per session
    unique("idx_unique_academicSession_status").on(
      table.academicSessionId,
      table.status,
    ),
  ],
);

// ==================== RELATIONS ====================

/**
 * Type definitions for the student schema tables
 */
export type Guardian = typeof guardianTable.$inferSelect;
export type NewGuardian = typeof guardianTable.$inferInsert;

export type Student = typeof studentTable.$inferSelect;
export type NewStudent = typeof studentTable.$inferInsert;

export type Enrollment = typeof enrollmentTable.$inferSelect;
export type NewEnrollment = typeof enrollmentTable.$inferInsert;
