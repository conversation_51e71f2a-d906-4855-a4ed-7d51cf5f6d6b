/**
 * Drizzle PostgreSQL Database Schemas
 *
 * This file exports all database schemas organized by domain/context.
 * Each schema file contains related tables and their type definitions.
 *
 * Schema Organization:
 * - auth.schema.ts: Authentication and user management
 * - platform.schema.ts: Platform administration and subscriptions
 * - institute.schema.ts: Institute management and branches
 * - academic.schema.ts: Academic sessions, classes, and subjects
 * - staff.schema.ts: Staff management and profiles
 * - student.schema.ts: Student management and enrollments
 * - education.schema.ts: Class sections, subject assignments, and diary
 */

import {
  academicSessionTable,
  classTable,
  subjectTable,
} from "./academic.schema.js";
import { roleTable, usersTable } from "./auth.schema.js";
import {
  classSectionTable,
  diaryTable,
  sectionSubjectTable,
} from "./education.schema.js";
import {
  branchTable,
  instituteOwnerTable,
  instituteTable,
} from "./institute.schema.js";
import {
  platformAdminTable,
  subscriptionPlanTable,
  subscriptionTable,
} from "./platform.schema.js";
import { staffTable, supportStaffProfileTable } from "./staff.schema.js";
import {
  enrollmentTable,
  guardianTable,
  studentTable,
} from "./student.schema.js";
import postgres from "postgres";
import { drizzle } from "drizzle-orm/postgres-js";

export const databaseSchema = {
  // Auth tables
  roleTable,
  usersTable,

  // Platform tables
  platformAdminTable,
  subscriptionPlanTable,
  subscriptionTable,

  // Institute tables
  instituteOwnerTable,
  instituteTable,
  branchTable,

  // Academic tables
  academicSessionTable,
  classTable,
  subjectTable,

  // Staff tables
  supportStaffProfileTable,
  staffTable,

  // Student tables
  guardianTable,
  studentTable,
  enrollmentTable,

  // Education tables
  classSectionTable,
  sectionSubjectTable,
  diaryTable,
};

