import {
  pgTable,
  uuid,
  text,
  boolean,
  timestamp,
  date,
  unique,
  index,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import {
  classTable,
  subjectTable,
  academicSessionTable,
} from "./academic.schema.js";
import { staffTable } from "./staff.schema.js";

// ==================== TABLES ====================

/**
 * Class sections table
 * Divisions/sections within each class
 */
export const classSectionTable = pgTable(
  "classSection",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    name: text("name").notNull(),
    classId: uuid("classId")
      .notNull()
      .references(() => classTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    classTeacherId: uuid("classTeacherId")
      .notNull()
      .references(() => staffTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    isActive: boolean("isActive").notNull().default(true),
    createdAt: timestamp("createdAt", { withTimezone: true })
      .notNull()
      .default(sql`now()`),
  },
  table => [
    // Unique constraint for section name within class
    unique("classSection_name_classId_unique").on(table.name, table.classId),
    // Index for class queries
    index("idx_classSection_classId").on(table.classId),
  ],
);

/**
 * Section subjects table
 * Assignment of subjects to class sections with teachers
 */
export const sectionSubjectTable = pgTable(
  "sectionSubject",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    classSectionId: uuid("classSectionId")
      .notNull()
      .references(() => classSectionTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    subjectId: uuid("subjectId")
      .notNull()
      .references(() => subjectTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    academicSessionId: uuid("academicSessionId")
      .notNull()
      .references(() => academicSessionTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    subjectTeacherId: uuid("subjectTeacherId")
      .notNull()
      .references(() => staffTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    createdAt: timestamp("createdAt", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Unique constraint for subject per class section
    unique("unique_subject_per_class_section").on(
      table.subjectId,
      table.classSectionId,
      table.academicSessionId,
    ),
    // Index for academic session queries
    index("idx_academicSessionId").on(table.academicSessionId),
  ],
);

/**
 * Diary table
 * Daily diary entries for class sections and subjects
 */
export const diaryTable = pgTable(
  "diary",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    classSectionId: uuid("classSectionId")
      .notNull()
      .references(() => classSectionTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    subjectId: uuid("subjectId")
      .notNull()
      .references(() => subjectTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    content: text("content").notNull(),
    date: date("date").notNull(),
    createdAt: timestamp("createdAt", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Unique constraint for diary entry per section, subject, and date
    unique("unique_diary_entry").on(
      table.classSectionId,
      table.subjectId,
      table.date,
    ),
  ],
);

// ==================== RELATIONS ====================

/**
 * Type definitions for the education schema tables
 */
export type ClassSection = typeof classSectionTable.$inferSelect;
export type NewClassSection = typeof classSectionTable.$inferInsert;

export type SectionSubject = typeof sectionSubjectTable.$inferSelect;
export type NewSectionSubject = typeof sectionSubjectTable.$inferInsert;

export type Diary = typeof diaryTable.$inferSelect;
export type NewDiary = typeof diaryTable.$inferInsert;
