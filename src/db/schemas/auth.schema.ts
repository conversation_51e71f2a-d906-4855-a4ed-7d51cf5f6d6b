import {
  pgTable,
  uuid,
  text,
  integer,
  boolean,
  timestamp,
  pgEnum,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";

// ==================== ENUMS ====================

/**
 * Gender enumeration for user profiles
 */
export const genderEnum = pgEnum("gender", ["MALE", "FEMALE", "OTHER"]);

/**
 * Role name enumeration for different user types in the system
 */
export const roleNameEnum = pgEnum("roleName", [
  "BRANCH_ADMIN",
  "ACCOUNTANT",
  "PLATFORM_ADMIN",
  "INSTITUTE_OWNER",
  "TEACHER",
  "SUPPORT_STAFF",
  "STUDENT",
  "GUARDIAN",
]);

// ==================== TABLES ====================

/**
 * Role table defining different user roles in the system
 * Each role has a unique name, code, and description
 */
export const roleTable = pgTable("role", {
  id: uuid("id")
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  name: roleNameEnum("name").notNull().unique(),
  code: integer("code").notNull().unique(),
  description: text("description").notNull(),
  createdAt: timestamp("createdAt", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});

/**
 * Users table storing core user information
 * Central table for all user types in the system
 */
export const usersTable = pgTable("users", {
  id: uuid("id")
    .primaryKey()
    .default(sql`gen_random_uuid()`),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  phone: text("phone").notNull().unique(),
  address: text("address").notNull(),
  gender: genderEnum("gender").notNull(),
  photo: text("photo"),
  roleId: uuid("roleId")
    .notNull()
    .references(() => roleTable.id, {
      onDelete: "restrict",
      onUpdate: "cascade",
    }),
  cnic: text("cnic").notNull().unique(),
  isActive: boolean("isActive").notNull().default(true),
  password: text("password").notNull(),
  isPasswordTemporary: boolean("isPasswordTemporary").notNull().default(false),
  createdAt: timestamp("createdAt", { withTimezone: true })
    .notNull()
    .default(sql`NOW()`),
});

// ==================== RELATIONS ====================

/**
 * Type definitions for the auth schema tables
 */
export type Role = typeof roleTable.$inferSelect;
export type NewRole = typeof roleTable.$inferInsert;

export type User = typeof usersTable.$inferSelect;
export type NewUser = typeof usersTable.$inferInsert;
