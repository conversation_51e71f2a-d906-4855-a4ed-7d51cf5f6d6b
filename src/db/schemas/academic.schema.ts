import {
  pgTable,
  uuid,
  text,
  date,
  boolean,
  timestamp,
  numeric,
  integer,
  unique,
  index,
  check,
  pgEnum,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { branchTable } from "./institute.schema.js";

// ==================== ENUMS ====================

/**
 * Subject type enumeration for academic subjects
 */
export const subjectTypeEnum = pgEnum("subjectType", ["THEORY", "PRACTICAL"]);

// ==================== TABLES ====================

/**
 * Academic sessions table
 * Defines academic years/terms for each branch
 */
export const academicSessionTable = pgTable(
  "academicSession",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    name: text("name").notNull(),
    startDate: date("startDate").notNull(),
    endDate: date("endDate").notNull(),
    branchId: uuid("branchId")
      .notNull()
      .references(() => branchTable.id),
    isActive: boolean("isActive").notNull().default(false),
    createdAt: timestamp("createdAt", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Check constraint to ensure start date is before end date
    check(
      "start_date_before_end_date",
      sql`${table.startDate} < ${table.endDate}`,
    ),
    // Unique constraint for academic session dates
    unique("academic_session_date_unique").on(table.startDate, table.endDate),
    // Unique index to ensure only one active session per branch
    unique("academic_session_isActive_index").on(
      table.branchId,
      table.isActive,
    ),
  ],
);

/**
 * Classes table
 * Academic classes/grades within each session
 */
export const classTable = pgTable(
  "class",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    name: text("name").notNull(),
    maximumStudents: integer("maximumStudents").notNull(),
    isActive: boolean("isActive").notNull().default(true),
    academicSessionId: uuid("academicSessionId")
      .notNull()
      .references(() => academicSessionTable.id, {
        onDelete: "restrict",
        onUpdate: "cascade",
      }),
    feePerMonth: numeric("feePerMonth", { precision: 10, scale: 2 }).notNull(),
    createdAt: timestamp("createdAt", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Unique constraint for class name within academic session
    unique("class_name_academicSessionId_unique").on(
      table.name,
      table.academicSessionId,
    ),
  ],
);

/**
 * Subjects table
 * Academic subjects for each session
 */
export const subjectTable = pgTable(
  "subject",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    name: text("name").notNull(),
    marks: integer("marks").notNull(),
    isActive: boolean("isActive").notNull().default(true),
    academicSessionId: uuid("academicSessionId")
      .notNull()
      .references(() => academicSessionTable.id),
    type: subjectTypeEnum("type").notNull(),
    createdAt: timestamp("createdAt", { withTimezone: true })
      .notNull()
      .default(sql`NOW()`),
  },
  table => [
    // Unique index for subject name, session, and type combination
    unique("idx_subject_name_sessionId_type").on(
      table.name,
      table.academicSessionId,
      table.type,
    ),
    // Index for academic session queries
    index("idx_subject_sessionId").on(table.academicSessionId),
  ],
);

// ==================== RELATIONS ====================

/**
 * Type definitions for the academic schema tables
 */
export type AcademicSession = typeof academicSessionTable.$inferSelect;
export type NewAcademicSession = typeof academicSessionTable.$inferInsert;

export type Class = typeof classTable.$inferSelect;
export type NewClass = typeof classTable.$inferInsert;

export type Subject = typeof subjectTable.$inferSelect;
export type NewSubject = typeof subjectTable.$inferInsert;
