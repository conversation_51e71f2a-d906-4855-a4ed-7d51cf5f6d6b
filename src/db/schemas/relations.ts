import { relations } from "drizzle-orm";
import { roleTable, usersTable } from "./auth.schema.js";
import {
  branchTable,
  instituteOwnerTable,
  instituteTable,
} from "./institute.schema.js";
import {
  platformAdminTable,
  subscriptionPlanTable,
  subscriptionTable,
} from "./platform.schema.js";
import {
  enrollmentTable,
  guardianTable,
  studentTable,
} from "./student.schema.js";
import { staffTable, supportStaffProfileTable } from "./staff.schema.js";
import {
  academicSessionTable,
  classTable,
  subjectTable,
} from "./academic.schema.js";
import {
  classSectionTable,
  diaryTable,
  sectionSubjectTable,
} from "./education.schema.js";

// ==================== AUTH RELATIONS ====================

export const roleRelations = relations(roleTable, ({ many }) => ({
  users: many(usersTable),
}));

export const usersRelations = relations(usersTable, ({ one, many }) => ({
  role: one(roleTable, {
    fields: [usersTable.roleId],
    references: [roleTable.id],
  }),
  instituteOwner: one(instituteOwnerTable),
  platformAdmin: one(platformAdminTable),
  guardian: one(guardianTable),
  staff: many(staffTable),
}));

// ==================== PLATFORM RELATIONS ====================

export const platformAdminRelations = relations(
  platformAdminTable,
  ({ one }) => ({
    user: one(usersTable, {
      fields: [platformAdminTable.userId],
      references: [usersTable.id],
    }),
  }),
);

export const subscriptionPlanRelations = relations(
  subscriptionPlanTable,
  ({ many }) => ({
    subscriptions: many(subscriptionTable),
  }),
);

export const subscriptionRelations = relations(
  subscriptionTable,
  ({ one }) => ({
    plan: one(subscriptionPlanTable, {
      fields: [subscriptionTable.planId],
      references: [subscriptionPlanTable.id],
    }),
    owner: one(instituteOwnerTable, {
      fields: [subscriptionTable.ownerId],
      references: [instituteOwnerTable.userId],
    }),
  }),
);

// ==================== INSTITUTE RELATIONS ====================

export const instituteOwnerRelations = relations(
  instituteOwnerTable,
  ({ one }) => ({
    user: one(usersTable, {
      fields: [instituteOwnerTable.userId],
      references: [usersTable.id],
    }),
    institute: one(instituteTable),
    subscription: one(subscriptionTable),
  }),
);

export const instituteRelations = relations(
  instituteTable,
  ({ one, many }) => ({
    owner: one(instituteOwnerTable, {
      fields: [instituteTable.ownerId],
      references: [instituteOwnerTable.userId],
    }),
    branches: many(branchTable),
  }),
);

export const branchRelations = relations(branchTable, ({ one, many }) => ({
  institute: one(instituteTable, {
    fields: [branchTable.instituteId],
    references: [instituteTable.id],
  }),
  academicSessions: many(academicSessionTable),
  staff: many(staffTable),
}));

// ==================== ACADEMIC RELATIONS ====================

export const academicSessionRelations = relations(
  academicSessionTable,
  ({ one, many }) => ({
    branch: one(branchTable, {
      fields: [academicSessionTable.branchId],
      references: [branchTable.id],
    }),
    classes: many(classTable),
    subjects: many(subjectTable),
    enrollments: many(enrollmentTable),
    sectionSubjects: many(sectionSubjectTable),
  }),
);

export const classRelations = relations(classTable, ({ one, many }) => ({
  academicSession: one(academicSessionTable, {
    fields: [classTable.academicSessionId],
    references: [academicSessionTable.id],
  }),
  sections: many(classSectionTable),
}));

export const subjectRelations = relations(subjectTable, ({ one, many }) => ({
  academicSession: one(academicSessionTable, {
    fields: [subjectTable.academicSessionId],
    references: [academicSessionTable.id],
  }),
  sectionSubjects: many(sectionSubjectTable),
  diaryEntries: many(diaryTable),
}));

// ==================== STAFF RELATIONS ====================

export const supportStaffProfileRelations = relations(
  supportStaffProfileTable,
  ({ many }) => ({
    staff: many(staffTable),
  }),
);

export const staffRelations = relations(staffTable, ({ one, many }) => ({
  user: one(usersTable, {
    fields: [staffTable.userId],
    references: [usersTable.id],
  }),
  branch: one(branchTable, {
    fields: [staffTable.branchId],
    references: [branchTable.id],
  }),
  supportStaffProfile: one(supportStaffProfileTable, {
    fields: [staffTable.supportStaffProfileId],
    references: [supportStaffProfileTable.id],
  }),
  classSections: many(classSectionTable),
  sectionSubjects: many(sectionSubjectTable),
}));

// ==================== STUDENT RELATIONS ====================

export const guardianRelations = relations(guardianTable, ({ one, many }) => ({
  user: one(usersTable, {
    fields: [guardianTable.userId],
    references: [usersTable.id],
  }),
  students: many(studentTable),
}));

export const studentRelations = relations(studentTable, ({ one, many }) => ({
  guardian: one(guardianTable, {
    fields: [studentTable.guardianId],
    references: [guardianTable.userId],
  }),
  classSection: one(classSectionTable, {
    fields: [studentTable.classSectionId],
    references: [classSectionTable.id],
  }),
  enrollments: many(enrollmentTable),
}));

export const enrollmentRelations = relations(enrollmentTable, ({ one }) => ({
  student: one(studentTable, {
    fields: [enrollmentTable.studentId],
    references: [studentTable.id],
  }),
  classSection: one(classSectionTable, {
    fields: [enrollmentTable.classSectionId],
    references: [classSectionTable.id],
  }),
  academicSession: one(academicSessionTable, {
    fields: [enrollmentTable.academicSessionId],
    references: [academicSessionTable.id],
  }),
}));

// ==================== EDUCATION RELATIONS ====================

export const classSectionRelations = relations(
  classSectionTable,
  ({ one, many }) => ({
    class: one(classTable, {
      fields: [classSectionTable.classId],
      references: [classTable.id],
    }),
    classTeacher: one(staffTable, {
      fields: [classSectionTable.classTeacherId],
      references: [staffTable.id],
    }),
    students: many(studentTable),
    enrollments: many(enrollmentTable),
    sectionSubjects: many(sectionSubjectTable),
    diaryEntries: many(diaryTable),
  }),
);

export const sectionSubjectRelations = relations(
  sectionSubjectTable,
  ({ one }) => ({
    classSection: one(classSectionTable, {
      fields: [sectionSubjectTable.classSectionId],
      references: [classSectionTable.id],
    }),
    subject: one(subjectTable, {
      fields: [sectionSubjectTable.subjectId],
      references: [subjectTable.id],
    }),
    academicSession: one(academicSessionTable, {
      fields: [sectionSubjectTable.academicSessionId],
      references: [academicSessionTable.id],
    }),
    subjectTeacher: one(staffTable, {
      fields: [sectionSubjectTable.subjectTeacherId],
      references: [staffTable.id],
    }),
  }),
);

export const diaryRelations = relations(diaryTable, ({ one }) => ({
  classSection: one(classSectionTable, {
    fields: [diaryTable.classSectionId],
    references: [classSectionTable.id],
  }),
  subject: one(subjectTable, {
    fields: [diaryTable.subjectId],
    references: [subjectTable.id],
  }),
}));
