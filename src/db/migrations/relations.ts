import { relations } from "drizzle-orm/relations";
import {
  role,
  users,
  instituteOwner,
  institute,
  branch,
  platformAdmin,
  subscriptionPlan,
  subscription,
  academicSession,
  classSection,
  diary,
  subject,
  staff,
  supportStaffProfile,
  classTable,
  guardian,
  student,
  enrollment,
  sectionSubject,
} from "./schema.js";

export const usersRelations = relations(users, ({ one, many }) => ({
  role: one(role, {
    fields: [users.roleId],
    references: [role.id],
  }),
  instituteOwners: many(instituteOwner),
  platformAdmins: many(platformAdmin),
  staff: many(staff),
  guardians: many(guardian),
}));

export const roleRelations = relations(role, ({ many }) => ({
  users: many(users),
}));

export const instituteOwnerRelations = relations(
  instituteOwner,
  ({ one, many }) => ({
    user: one(users, {
      fields: [instituteOwner.userId],
      references: [users.id],
    }),
    institutes: many(institute),
    subscriptions: many(subscription),
  }),
);

export const instituteRelations = relations(institute, ({ one, many }) => ({
  instituteOwner: one(instituteOwner, {
    fields: [institute.ownerId],
    references: [instituteOwner.userId],
  }),
  branches: many(branch),
}));

export const branchRelations = relations(branch, ({ one, many }) => ({
  institute: one(institute, {
    fields: [branch.instituteId],
    references: [institute.id],
  }),
  academicSessions: many(academicSession),
  staff: many(staff),
}));

export const platformAdminRelations = relations(platformAdmin, ({ one }) => ({
  user: one(users, {
    fields: [platformAdmin.userId],
    references: [users.id],
  }),
}));

export const subscriptionRelations = relations(subscription, ({ one }) => ({
  subscriptionPlan: one(subscriptionPlan, {
    fields: [subscription.planId],
    references: [subscriptionPlan.id],
  }),
  instituteOwner: one(instituteOwner, {
    fields: [subscription.ownerId],
    references: [instituteOwner.userId],
  }),
}));

export const subscriptionPlanRelations = relations(
  subscriptionPlan,
  ({ many }) => ({
    subscriptions: many(subscription),
  }),
);

export const academicSessionRelations = relations(
  academicSession,
  ({ one, many }) => ({
    branch: one(branch, {
      fields: [academicSession.branchId],
      references: [branch.id],
    }),
    subjects: many(subject),
    classes: many(classTable),
    enrollments: many(enrollment),
    sectionSubjects: many(sectionSubject),
  }),
);

export const diaryRelations = relations(diary, ({ one }) => ({
  classSection: one(classSection, {
    fields: [diary.classSectionId],
    references: [classSection.id],
  }),
  subject: one(subject, {
    fields: [diary.subjectId],
    references: [subject.id],
  }),
}));

export const classSectionRelations = relations(
  classSection,
  ({ one, many }) => ({
    diaries: many(diary),
    class: one(classTable, {
      fields: [classSection.classId],
      references: [classTable.id],
    }),
    staff: one(staff, {
      fields: [classSection.classTeacherId],
      references: [staff.id],
    }),
    students: many(student),
    enrollments: many(enrollment),
    sectionSubjects: many(sectionSubject),
  }),
);

export const subjectRelations = relations(subject, ({ one, many }) => ({
  diaries: many(diary),
  academicSession: one(academicSession, {
    fields: [subject.academicSessionId],
    references: [academicSession.id],
  }),
  sectionSubjects: many(sectionSubject),
}));

export const staffRelations = relations(staff, ({ one, many }) => ({
  user: one(users, {
    fields: [staff.userId],
    references: [users.id],
  }),
  branch: one(branch, {
    fields: [staff.branchId],
    references: [branch.id],
  }),
  supportStaffProfile: one(supportStaffProfile, {
    fields: [staff.supportStaffProfileId],
    references: [supportStaffProfile.id],
  }),
  classSections: many(classSection),
  sectionSubjects: many(sectionSubject),
}));

export const supportStaffProfileRelations = relations(
  supportStaffProfile,
  ({ many }) => ({
    staff: many(staff),
  }),
);

export const classRelations = relations(classTable, ({ one, many }) => ({
  academicSession: one(academicSession, {
    fields: [classTable.academicSessionId],
    references: [academicSession.id],
  }),
  classSections: many(classSection),
}));

export const guardianRelations = relations(guardian, ({ one, many }) => ({
  user: one(users, {
    fields: [guardian.userId],
    references: [users.id],
  }),
  students: many(student),
}));

export const studentRelations = relations(student, ({ one, many }) => ({
  classSection: one(classSection, {
    fields: [student.classSectionId],
    references: [classSection.id],
  }),
  guardian: one(guardian, {
    fields: [student.guardianId],
    references: [guardian.userId],
  }),
  enrollments: many(enrollment),
}));

export const enrollmentRelations = relations(enrollment, ({ one }) => ({
  student: one(student, {
    fields: [enrollment.studentId],
    references: [student.id],
  }),
  classSection: one(classSection, {
    fields: [enrollment.classSectionId],
    references: [classSection.id],
  }),
  academicSession: one(academicSession, {
    fields: [enrollment.academicSessionId],
    references: [academicSession.id],
  }),
}));

export const sectionSubjectRelations = relations(sectionSubject, ({ one }) => ({
  classSection: one(classSection, {
    fields: [sectionSubject.classSectionId],
    references: [classSection.id],
  }),
  subject: one(subject, {
    fields: [sectionSubject.subjectId],
    references: [subject.id],
  }),
  academicSession: one(academicSession, {
    fields: [sectionSubject.academicSessionId],
    references: [academicSession.id],
  }),
  staff: one(staff, {
    fields: [sectionSubject.subjectTeacherId],
    references: [staff.id],
  }),
}));
