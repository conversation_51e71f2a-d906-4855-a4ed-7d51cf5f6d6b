import {
  pgTable,
  varchar,
  integer,
  unique,
  uuid,
  text,
  timestamp,
  foreignKey,
  boolean,
  date,
  uniqueIndex,
  numeric,
  check,
  index,
  pgEnum,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import * as relations from "./relations.js";

export const enrollmentStatus = pgEnum("enrollment_status", [
  "ACTIVE",
  "EXPELLED",
  "GRADUATED",
  "DECEASED",
  "COMPLETED",
  "WITHDRAWN",
]);
export const enrollmentType = pgEnum("enrollment_type", [
  "ADMISSION",
  "TRANSFER_IN",
  "REPEATING",
  "PROMOTION",
]);
export const gender = pgEnum("gender", ["MALE", "FEMALE", "OTHER"]);
export const guardianRelation = pgEnum("guardian_relation", [
  "FATHER",
  "MOTHER",
  "GUARDIAN",
]);
export const religion = pgEnum("religion", [
  "ISLAM",
  "CHRISTIANITY",
  "HINDUISM",
  "BUDDHISM",
  "SIKHISM",
  "JUDAISM",
  "OTHER",
]);
export const roleName = pgEnum("role_name", [
  "BRANCH_ADMIN",
  "ACCOUNTANT",
  "PLATFORM_ADMIN",
  "INSTITUTE_OWNER",
  "TEACHER",
  "SUPPORT_STAFF",
  "STUDENT",
  "GUARDIAN",
]);
export const staffDepartment = pgEnum("staff_department", [
  "ACADEMIC",
  "ADMINISTRATION",
  "SUPPORT",
]);
export const staffType = pgEnum("staff_type", [
  "TEACHER",
  "SUPPORT_STAFF",
  "BRANCH_ADMIN",
  "ACCOUNTANT",
]);
export const subjectType = pgEnum("subject_type", ["THEORY", "PRACTICAL"]);

export const kyselyMigration = pgTable("kysely_migration", {
  name: varchar({ length: 255 }).primaryKey().notNull(),
  timestamp: varchar({ length: 255 }).notNull(),
});

export const kyselyMigrationLock = pgTable("kysely_migration_lock", {
  id: varchar({ length: 255 }).primaryKey().notNull(),
  isLocked: integer("is_locked").default(0).notNull(),
});

export const role = pgTable(
  "role",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    name: roleName().notNull(),
    code: integer().notNull(),
    description: text().notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    unique("role_name_key").on(table.name),
    unique("role_code_key").on(table.code),
  ],
);

export const users = pgTable(
  "users",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    name: text().notNull(),
    email: text().notNull(),
    phone: text().notNull(),
    address: text().notNull(),
    gender: gender().notNull(),
    photo: text(),
    roleId: uuid("role_id").notNull(),
    cnic: text().notNull(),
    isActive: boolean("is_active").default(true).notNull(),
    password: text().notNull(),
    isPasswordTemporary: boolean("is_password_temporary")
      .default(false)
      .notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    foreignKey({
      columns: [table.roleId],
      foreignColumns: [role.id],
      name: "users_role_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    unique("users_email_key").on(table.email),
    unique("users_phone_key").on(table.phone),
    unique("users_cnic_key").on(table.cnic),
  ],
);

export const test = pgTable("test", {
  name: text().notNull(),
  date: date().notNull(),
});

export const instituteOwner = pgTable(
  "institute_owner",
  {
    userId: uuid("user_id").primaryKey().notNull(),
    hasCompletedSetup: boolean("has_completed_setup").default(false).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.id],
      name: "owner_user_id",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
  ],
);

export const institute = pgTable(
  "institute",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    name: text().notNull(),
    email: text().notNull(),
    logo: text(),
    isActive: boolean("is_active").default(true).notNull(),
    isBasicSetupComplete: boolean("is_basic_setup_complete")
      .default(false)
      .notNull(),
    ownerId: uuid("owner_id").notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    foreignKey({
      columns: [table.ownerId],
      foreignColumns: [instituteOwner.userId],
      name: "institute_owner_id",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    unique("institute_name_key").on(table.name),
    unique("institute_email_key").on(table.email),
    unique("institute_owner_id_key").on(table.ownerId),
  ],
);

export const branch = pgTable(
  "branch",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    email: text().notNull(),
    name: text().notNull(),
    address: text().notNull(),
    phone: text().notNull(),
    isMain: boolean("is_main").default(false).notNull(),
    isActive: boolean("is_active").default(true).notNull(),
    instituteId: uuid("institute_id").notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    uniqueIndex("branch_is_main_unique_index")
      .using(
        "btree",
        table.isMain.asc().nullsLast().op("bool_ops"),
        table.instituteId.asc().nullsLast().op("bool_ops"),
      )
      .where(sql`(is_main = true)`),
    foreignKey({
      columns: [table.instituteId],
      foreignColumns: [institute.id],
      name: "branch_institute_id_fkey",
    }),
    unique("branch_name_key").on(table.name),
  ],
);

export const platformAdmin = pgTable(
  "platform_admin",
  {
    userId: uuid("user_id").primaryKey().notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.id],
      name: "admin_user_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
  ],
);

export const subscriptionPlan = pgTable(
  "subscription_plan",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    title: text().notNull(),
    description: text(),
    price: numeric({ precision: 10, scale: 2 }).notNull(),
    setupCharges: numeric("setup_charges", {
      precision: 10,
      scale: 2,
    }).notNull(),
    branches: integer().notNull(),
    students: integer().notNull(),
    features: text().array().notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    unique("subscription_plan_title_key").on(table.title),
    unique("subscription_plan_price_key").on(table.price),
  ],
);

export const subscription = pgTable(
  "subscription",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    planId: uuid("plan_id").notNull(),
    ownerId: uuid("owner_id").notNull(),
    status: varchar({ length: 20 }).notNull(),
    startDate: timestamp("start_date", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
    paymentCycle: varchar("payment_cycle", { length: 20 }).notNull(),
    gracePeriodDays: integer("grace_period_days").default(5).notNull(),
    endDate: timestamp("end_date", {
      withTimezone: true,
      mode: "string",
    }).notNull(),
    nextPaymentDate: timestamp("next_payment_date", {
      withTimezone: true,
      mode: "string",
    }).notNull(),
    lastPaymentDate: timestamp("last_payment_date", {
      withTimezone: true,
      mode: "string",
    })
      .defaultNow()
      .notNull(),
    cancellationDate: timestamp("cancellation_date", {
      withTimezone: true,
      mode: "string",
    }),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    foreignKey({
      columns: [table.planId],
      foreignColumns: [subscriptionPlan.id],
      name: "subscription_plan_id_fkey",
    }),
    foreignKey({
      columns: [table.ownerId],
      foreignColumns: [instituteOwner.userId],
      name: "subscription_user_id_fkey",
    }),
    unique("subscription_owner_id_key").on(table.ownerId),
    check(
      "subscription_status_check",
      sql`(status)::text = ANY ((ARRAY['active'::character varying, 'cancelled'::character varying, 'unpaid'::character varying, 'expired'::character varying])::text[])`,
    ),
    check(
      "subscription_payment_cycle_check",
      sql`(payment_cycle)::text = ANY ((ARRAY['monthly'::character varying, 'yearly'::character varying])::text[])`,
    ),
  ],
);

export const academicSession = pgTable(
  "academic_session",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    name: text().notNull(),
    startDate: date("start_date").notNull(),
    endDate: date("end_date").notNull(),
    branchId: uuid("branch_id").notNull(),
    isActive: boolean("is_active").default(false).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    uniqueIndex("academic_session_is_active_index")
      .using(
        "btree",
        table.branchId.asc().nullsLast().op("bool_ops"),
        table.isActive.asc().nullsLast().op("bool_ops"),
      )
      .where(sql`(is_active = true)`),
    foreignKey({
      columns: [table.branchId],
      foreignColumns: [branch.id],
      name: "academic_session_branch_id_fkey",
    }),
    unique("academic_session_date_unique").on(table.startDate, table.endDate),
    check("start_date_before_end_date", sql`start_date < end_date`),
  ],
);

export const diary = pgTable(
  "diary",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    classSectionId: uuid("class_section_id").notNull(),
    subjectId: uuid("subject_id").notNull(),
    content: text().notNull(),
    date: date().notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    foreignKey({
      columns: [table.classSectionId],
      foreignColumns: [classSection.id],
      name: "diary_class_section_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    foreignKey({
      columns: [table.subjectId],
      foreignColumns: [subject.id],
      name: "diary_subject_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    unique("unique_diary_entry").on(
      table.classSectionId,
      table.subjectId,
      table.date,
    ),
  ],
);

export const staff = pgTable(
  "staff",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    userId: uuid("user_id"),
    branchId: uuid("branch_id").notNull(),
    supportStaffProfileId: uuid("support_staff_profile_id"),
    designation: text().notNull(),
    department: staffDepartment().notNull(),
    salary: numeric({ precision: 10, scale: 2 }).notNull(),
    type: staffType().notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    uniqueIndex("staff_support_staff_profile_id_unique_index").using(
      "btree",
      table.supportStaffProfileId.asc().nullsLast().op("uuid_ops"),
      table.branchId.asc().nullsLast().op("uuid_ops"),
    ),
    uniqueIndex("staff_user_id_unique_index").using(
      "btree",
      table.userId.asc().nullsLast().op("uuid_ops"),
      table.branchId.asc().nullsLast().op("uuid_ops"),
    ),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.id],
      name: "staff_user_id_fkey",
    }),
    foreignKey({
      columns: [table.branchId],
      foreignColumns: [branch.id],
      name: "staff_branch_id_fkey",
    }),
    foreignKey({
      columns: [table.supportStaffProfileId],
      foreignColumns: [supportStaffProfile.id],
      name: "staff_support_staff_profile_id_fkey",
    }),
    unique("staff_user_id_key").on(table.userId),
    unique("staff_support_staff_profile_id_key").on(
      table.supportStaffProfileId,
    ),
    check(
      "staff_is_either_user_or_support_staff",
      sql`((user_id IS NOT NULL) AND (support_staff_profile_id IS NULL)) OR ((user_id IS NULL) AND (support_staff_profile_id IS NOT NULL))`,
    ),
  ],
);

export const supportStaffProfile = pgTable(
  "support_staff_profile",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    name: text().notNull(),
    email: text().notNull(),
    phone: text().notNull(),
    address: text().notNull(),
    gender: gender().notNull(),
    photo: text(),
    cnic: text().notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .default("2025-05-29 15:17:29.29808+05")
      .notNull(),
  },
  table => [
    unique("support_staff_profile_email_key").on(table.email),
    unique("support_staff_profile_phone_key").on(table.phone),
    unique("support_staff_profile_cnic_key").on(table.cnic),
  ],
);

export const subject = pgTable(
  "subject",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    name: text().notNull(),
    marks: integer().notNull(),
    isActive: boolean("is_active").default(true).notNull(),
    academicSessionId: uuid("academic_session_id").notNull(),
    type: subjectType().notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    uniqueIndex("idx_subject_name_session_id_type").using(
      "btree",
      table.name.asc().nullsLast().op("enum_ops"),
      table.academicSessionId.asc().nullsLast().op("enum_ops"),
      table.type.asc().nullsLast().op("uuid_ops"),
    ),
    index("idx_subject_session_id").using(
      "btree",
      table.academicSessionId.asc().nullsLast().op("uuid_ops"),
    ),
    foreignKey({
      columns: [table.academicSessionId],
      foreignColumns: [academicSession.id],
      name: "subject_academic_session_id_fkey",
    }),
  ],
);

export const classTable = pgTable(
  "class",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    name: text().notNull(),
    maximumStudents: integer("maximum_students").notNull(),
    isActive: boolean("is_active").default(true).notNull(),
    academicSessionId: uuid("academic_session_id").notNull(),
    feePerMonth: numeric("fee_per_month", {
      precision: 10,
      scale: 2,
    }).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    foreignKey({
      columns: [table.academicSessionId],
      foreignColumns: [academicSession.id],
      name: "class_academic_session_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    unique("class_name_academic_session_id_unique").on(
      table.name,
      table.academicSessionId,
    ),
  ],
);

export const classSection = pgTable(
  "class_section",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    name: text().notNull(),
    classId: uuid("class_id").notNull(),
    classTeacherId: uuid("class_teacher_id").notNull(),
    isActive: boolean("is_active").default(true).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    index("idx_class_section_class_id").using(
      "btree",
      table.classId.asc().nullsLast().op("uuid_ops"),
    ),
    foreignKey({
      columns: [table.classId],
      foreignColumns: [classTable.id],
      name: "class_section_class_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    foreignKey({
      columns: [table.classTeacherId],
      foreignColumns: [staff.id],
      name: "class_section_class_teacher_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    unique("class_section_name_class_id_unique").on(table.name, table.classId),
  ],
);

export const guardian = pgTable(
  "guardian",
  {
    userId: uuid("user_id").primaryKey().notNull(),
    relation: guardianRelation().notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.id],
      name: "guardian_user_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
  ],
);

export const student = pgTable(
  "student",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    name: text().notNull(),
    registrationNumber: text("registration_number"),
    email: text(),
    fatherName: text("father_name").notNull(),
    address: text().notNull(),
    gender: gender().notNull(),
    photo: text(),
    religion: religion().notNull(),
    monthlyFee: numeric("monthly_fee", { precision: 10, scale: 2 }).notNull(),
    admissionDate: date("admission_date").notNull(),
    dateOfBirth: date("date_of_birth").notNull(),
    previousSchool: text("previous_school"),
    classSectionId: uuid("class_section_id").notNull(),
    guardianId: uuid("guardian_id").notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    foreignKey({
      columns: [table.classSectionId],
      foreignColumns: [classSection.id],
      name: "student_class_section_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    foreignKey({
      columns: [table.guardianId],
      foreignColumns: [guardian.userId],
      name: "student_guardian_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    unique("student_email_key").on(table.email),
  ],
);

export const enrollment = pgTable(
  "enrollment",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    studentId: uuid("student_id").notNull(),
    classSectionId: uuid("class_section_id").notNull(),
    academicSessionId: uuid("academic_session_id").notNull(),
    type: enrollmentType().notNull(),
    status: enrollmentStatus().notNull(),
    date: date().notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    uniqueIndex("idx_unique_academic_session_status")
      .using(
        "btree",
        table.academicSessionId.asc().nullsLast().op("uuid_ops"),
        table.status.asc().nullsLast().op("enum_ops"),
      )
      .where(sql`(status = 'ACTIVE'::enrollment_status)`),
    uniqueIndex("idx_unique_student_per_section_if_not_repeating")
      .using(
        "btree",
        table.studentId.asc().nullsLast().op("enum_ops"),
        table.classSectionId.asc().nullsLast().op("uuid_ops"),
        table.type.asc().nullsLast().op("enum_ops"),
      )
      .where(sql`(type <> 'REPEATING'::enrollment_type)`),
    foreignKey({
      columns: [table.studentId],
      foreignColumns: [student.id],
      name: "enrollment_student_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    foreignKey({
      columns: [table.classSectionId],
      foreignColumns: [classSection.id],
      name: "enrollment_class_section_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    foreignKey({
      columns: [table.academicSessionId],
      foreignColumns: [academicSession.id],
      name: "enrollment_academic_session_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    unique("unique_student_session_per_enrollment").on(
      table.studentId,
      table.academicSessionId,
    ),
  ],
);

export const sectionSubject = pgTable(
  "section_subject",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    classSectionId: uuid("class_section_id").notNull(),
    subjectId: uuid("subject_id").notNull(),
    academicSessionId: uuid("academic_session_id").notNull(),
    subjectTeacherId: uuid("subject_teacher_id").notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  table => [
    index("idx_academic_session_id").using(
      "btree",
      table.academicSessionId.asc().nullsLast().op("uuid_ops"),
    ),
    foreignKey({
      columns: [table.classSectionId],
      foreignColumns: [classSection.id],
      name: "section_subject_class_section_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    foreignKey({
      columns: [table.subjectId],
      foreignColumns: [subject.id],
      name: "section_subject_subject_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    foreignKey({
      columns: [table.academicSessionId],
      foreignColumns: [academicSession.id],
      name: "section_subject_academic_session_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    foreignKey({
      columns: [table.subjectTeacherId],
      foreignColumns: [staff.id],
      name: "section_subject_subject_teacher_id_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
    unique("unique_subject_per_class_section_per_session").on(
      table.classSectionId,
      table.subjectId,
      table.academicSessionId,
    ),
  ],
);

export const databaseSchema = {
  role,
  users,
  instituteOwner,
  institute,
  branch,
  platformAdmin,
  subscriptionPlan,
  subscription,
  academicSession,
  classTable,
  subject,
  staff,
  supportStaffProfile,
  classSection,
  guardian,
  student,
  enrollment,
  sectionSubject,
  diary,
};
