{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "7", "dialect": "postgresql", "tables": {"public.kysely_migration": {"name": "kysely_migration", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "timestamp": {"name": "timestamp", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.kysely_migration_lock": {"name": "kysely_migration_lock", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "is_locked": {"name": "is_locked", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.role": {"name": "role", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "role_name", "typeSchema": "public", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"role_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "role_name_key"}, "role_code_key": {"columns": ["code"], "nullsNotDistinct": false, "name": "role_code_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "gender": {"name": "gender", "type": "gender", "typeSchema": "public", "primaryKey": false, "notNull": true}, "photo": {"name": "photo", "type": "text", "primaryKey": false, "notNull": false}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}, "cnic": {"name": "cnic", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "is_password_temporary": {"name": "is_password_temporary", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"users_role_id_fkey": {"name": "users_role_id_fkey", "tableFrom": "users", "tableTo": "role", "schemaTo": "public", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_key": {"columns": ["email"], "nullsNotDistinct": false, "name": "users_email_key"}, "users_phone_key": {"columns": ["phone"], "nullsNotDistinct": false, "name": "users_phone_key"}, "users_cnic_key": {"columns": ["cnic"], "nullsNotDistinct": false, "name": "users_cnic_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.test": {"name": "test", "schema": "", "columns": {"name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.institute_owner": {"name": "institute_owner", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": true, "notNull": true}, "has_completed_setup": {"name": "has_completed_setup", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"owner_user_id": {"name": "owner_user_id", "tableFrom": "institute_owner", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.institute": {"name": "institute", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_basic_setup_complete": {"name": "is_basic_setup_complete", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "owner_id": {"name": "owner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"institute_owner_id": {"name": "institute_owner_id", "tableFrom": "institute", "tableTo": "institute_owner", "schemaTo": "public", "columnsFrom": ["owner_id"], "columnsTo": ["user_id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"institute_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "institute_name_key"}, "institute_email_key": {"columns": ["email"], "nullsNotDistinct": false, "name": "institute_email_key"}, "institute_owner_id_key": {"columns": ["owner_id"], "nullsNotDistinct": false, "name": "institute_owner_id_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.branch": {"name": "branch", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "is_main": {"name": "is_main", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "institute_id": {"name": "institute_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"branch_is_main_unique_index": {"name": "branch_is_main_unique_index", "columns": [{"expression": "is_main", "asc": true, "nulls": "last", "opclass": "bool_ops", "isExpression": false}, {"expression": "institute_id", "asc": true, "nulls": "last", "opclass": "bool_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "where": "(is_main = true)", "with": {}}}, "foreignKeys": {"branch_institute_id_fkey": {"name": "branch_institute_id_fkey", "tableFrom": "branch", "tableTo": "institute", "schemaTo": "public", "columnsFrom": ["institute_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"branch_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "branch_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.platform_admin": {"name": "platform_admin", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"admin_user_id_fkey": {"name": "admin_user_id_fkey", "tableFrom": "platform_admin", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.subscription_plan": {"name": "subscription_plan", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "setup_charges": {"name": "setup_charges", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "branches": {"name": "branches", "type": "integer", "primaryKey": false, "notNull": true}, "students": {"name": "students", "type": "integer", "primaryKey": false, "notNull": true}, "features": {"name": "features", "type": "text[]", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"subscription_plan_title_key": {"columns": ["title"], "nullsNotDistinct": false, "name": "subscription_plan_title_key"}, "subscription_plan_price_key": {"columns": ["price"], "nullsNotDistinct": false, "name": "subscription_plan_price_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.subscription": {"name": "subscription", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "owner_id": {"name": "owner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "payment_cycle": {"name": "payment_cycle", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "grace_period_days": {"name": "grace_period_days", "type": "integer", "primaryKey": false, "notNull": true, "default": 5}, "end_date": {"name": "end_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "next_payment_date": {"name": "next_payment_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "last_payment_date": {"name": "last_payment_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "cancellation_date": {"name": "cancellation_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"subscription_plan_id_fkey": {"name": "subscription_plan_id_fkey", "tableFrom": "subscription", "tableTo": "subscription_plan", "schemaTo": "public", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "subscription_user_id_fkey": {"name": "subscription_user_id_fkey", "tableFrom": "subscription", "tableTo": "institute_owner", "schemaTo": "public", "columnsFrom": ["owner_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"subscription_owner_id_key": {"columns": ["owner_id"], "nullsNotDistinct": false, "name": "subscription_owner_id_key"}}, "checkConstraints": {"subscription_status_check": {"name": "subscription_status_check", "value": "(status)::text = ANY ((ARRAY['active'::character varying, 'cancelled'::character varying, 'unpaid'::character varying, 'expired'::character varying])::text[])"}, "subscription_payment_cycle_check": {"name": "subscription_payment_cycle_check", "value": "(payment_cycle)::text = ANY ((ARRAY['monthly'::character varying, 'yearly'::character varying])::text[])"}}, "policies": {}, "isRLSEnabled": false}, "public.academic_session": {"name": "academic_session", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"academic_session_is_active_index": {"name": "academic_session_is_active_index", "columns": [{"expression": "branch_id", "asc": true, "nulls": "last", "opclass": "bool_ops", "isExpression": false}, {"expression": "is_active", "asc": true, "nulls": "last", "opclass": "bool_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "where": "(is_active = true)", "with": {}}}, "foreignKeys": {"academic_session_branch_id_fkey": {"name": "academic_session_branch_id_fkey", "tableFrom": "academic_session", "tableTo": "branch", "schemaTo": "public", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"academic_session_date_unique": {"columns": ["start_date", "end_date"], "nullsNotDistinct": false, "name": "academic_session_date_unique"}}, "checkConstraints": {"start_date_before_end_date": {"name": "start_date_before_end_date", "value": "start_date < end_date"}}, "policies": {}, "isRLSEnabled": false}, "public.diary": {"name": "diary", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "class_section_id": {"name": "class_section_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subject_id": {"name": "subject_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"diary_class_section_id_fkey": {"name": "diary_class_section_id_fkey", "tableFrom": "diary", "tableTo": "class_section", "schemaTo": "public", "columnsFrom": ["class_section_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "diary_subject_id_fkey": {"name": "diary_subject_id_fkey", "tableFrom": "diary", "tableTo": "subject", "schemaTo": "public", "columnsFrom": ["subject_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_diary_entry": {"columns": ["class_section_id", "subject_id", "date"], "nullsNotDistinct": false, "name": "unique_diary_entry"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.staff": {"name": "staff", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "support_staff_profile_id": {"name": "support_staff_profile_id", "type": "uuid", "primaryKey": false, "notNull": false}, "designation": {"name": "designation", "type": "text", "primaryKey": false, "notNull": true}, "department": {"name": "department", "type": "staff_department", "typeSchema": "public", "primaryKey": false, "notNull": true}, "salary": {"name": "salary", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "staff_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"staff_support_staff_profile_id_unique_index": {"name": "staff_support_staff_profile_id_unique_index", "columns": [{"expression": "support_staff_profile_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}, {"expression": "branch_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "staff_user_id_unique_index": {"name": "staff_user_id_unique_index", "columns": [{"expression": "user_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}, {"expression": "branch_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"staff_user_id_fkey": {"name": "staff_user_id_fkey", "tableFrom": "staff", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "staff_branch_id_fkey": {"name": "staff_branch_id_fkey", "tableFrom": "staff", "tableTo": "branch", "schemaTo": "public", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "staff_support_staff_profile_id_fkey": {"name": "staff_support_staff_profile_id_fkey", "tableFrom": "staff", "tableTo": "support_staff_profile", "schemaTo": "public", "columnsFrom": ["support_staff_profile_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"staff_user_id_key": {"columns": ["user_id"], "nullsNotDistinct": false, "name": "staff_user_id_key"}, "staff_support_staff_profile_id_key": {"columns": ["support_staff_profile_id"], "nullsNotDistinct": false, "name": "staff_support_staff_profile_id_key"}}, "checkConstraints": {"staff_is_either_user_or_support_staff": {"name": "staff_is_either_user_or_support_staff", "value": "((user_id IS NOT NULL) AND (support_staff_profile_id IS NULL)) OR ((user_id IS NULL) AND (support_staff_profile_id IS NOT NULL))"}}, "policies": {}, "isRLSEnabled": false}, "public.support_staff_profile": {"name": "support_staff_profile", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "gender": {"name": "gender", "type": "gender", "typeSchema": "public", "primaryKey": false, "notNull": true}, "photo": {"name": "photo", "type": "text", "primaryKey": false, "notNull": false}, "cnic": {"name": "cnic", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "'2025-05-29 15:17:29.29808+05'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"support_staff_profile_email_key": {"columns": ["email"], "nullsNotDistinct": false, "name": "support_staff_profile_email_key"}, "support_staff_profile_phone_key": {"columns": ["phone"], "nullsNotDistinct": false, "name": "support_staff_profile_phone_key"}, "support_staff_profile_cnic_key": {"columns": ["cnic"], "nullsNotDistinct": false, "name": "support_staff_profile_cnic_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.subject": {"name": "subject", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "marks": {"name": "marks", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "academic_session_id": {"name": "academic_session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "subject_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_subject_name_session_id_type": {"name": "idx_subject_name_session_id_type", "columns": [{"expression": "name", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}, {"expression": "academic_session_id", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}, {"expression": "type", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "idx_subject_session_id": {"name": "idx_subject_session_id", "columns": [{"expression": "academic_session_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"subject_academic_session_id_fkey": {"name": "subject_academic_session_id_fkey", "tableFrom": "subject", "tableTo": "academic_session", "schemaTo": "public", "columnsFrom": ["academic_session_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.class": {"name": "class", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "maximum_students": {"name": "maximum_students", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "academic_session_id": {"name": "academic_session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "fee_per_month": {"name": "fee_per_month", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"class_academic_session_id_fkey": {"name": "class_academic_session_id_fkey", "tableFrom": "class", "tableTo": "academic_session", "schemaTo": "public", "columnsFrom": ["academic_session_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"class_name_academic_session_id_unique": {"columns": ["name", "academic_session_id"], "nullsNotDistinct": false, "name": "class_name_academic_session_id_unique"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.class_section": {"name": "class_section", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "class_id": {"name": "class_id", "type": "uuid", "primaryKey": false, "notNull": true}, "class_teacher_id": {"name": "class_teacher_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_class_section_class_id": {"name": "idx_class_section_class_id", "columns": [{"expression": "class_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"class_section_class_id_fkey": {"name": "class_section_class_id_fkey", "tableFrom": "class_section", "tableTo": "class", "schemaTo": "public", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "class_section_class_teacher_id_fkey": {"name": "class_section_class_teacher_id_fkey", "tableFrom": "class_section", "tableTo": "staff", "schemaTo": "public", "columnsFrom": ["class_teacher_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"class_section_name_class_id_unique": {"columns": ["name", "class_id"], "nullsNotDistinct": false, "name": "class_section_name_class_id_unique"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.guardian": {"name": "guardian", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": true, "notNull": true}, "relation": {"name": "relation", "type": "guardian_relation", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"guardian_user_id_fkey": {"name": "guardian_user_id_fkey", "tableFrom": "guardian", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.student": {"name": "student", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "registration_number": {"name": "registration_number", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "father_name": {"name": "father_name", "type": "text", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "gender": {"name": "gender", "type": "gender", "typeSchema": "public", "primaryKey": false, "notNull": true}, "photo": {"name": "photo", "type": "text", "primaryKey": false, "notNull": false}, "religion": {"name": "religion", "type": "religion", "typeSchema": "public", "primaryKey": false, "notNull": true}, "monthly_fee": {"name": "monthly_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "admission_date": {"name": "admission_date", "type": "date", "primaryKey": false, "notNull": true}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": true}, "previous_school": {"name": "previous_school", "type": "text", "primaryKey": false, "notNull": false}, "class_section_id": {"name": "class_section_id", "type": "uuid", "primaryKey": false, "notNull": true}, "guardian_id": {"name": "guardian_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"student_class_section_id_fkey": {"name": "student_class_section_id_fkey", "tableFrom": "student", "tableTo": "class_section", "schemaTo": "public", "columnsFrom": ["class_section_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "student_guardian_id_fkey": {"name": "student_guardian_id_fkey", "tableFrom": "student", "tableTo": "guardian", "schemaTo": "public", "columnsFrom": ["guardian_id"], "columnsTo": ["user_id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"student_email_key": {"columns": ["email"], "nullsNotDistinct": false, "name": "student_email_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.enrollment": {"name": "enrollment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "class_section_id": {"name": "class_section_id", "type": "uuid", "primaryKey": false, "notNull": true}, "academic_session_id": {"name": "academic_session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "enrollment_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "enrollment_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_unique_academic_session_status": {"name": "idx_unique_academic_session_status", "columns": [{"expression": "academic_session_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}, {"expression": "status", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "where": "(status = 'ACTIVE'::enrollment_status)", "with": {}}, "idx_unique_student_per_section_if_not_repeating": {"name": "idx_unique_student_per_section_if_not_repeating", "columns": [{"expression": "student_id", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}, {"expression": "class_section_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}, {"expression": "type", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "where": "(type <> 'REPEATING'::enrollment_type)", "with": {}}}, "foreignKeys": {"enrollment_student_id_fkey": {"name": "enrollment_student_id_fkey", "tableFrom": "enrollment", "tableTo": "student", "schemaTo": "public", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "enrollment_class_section_id_fkey": {"name": "enrollment_class_section_id_fkey", "tableFrom": "enrollment", "tableTo": "class_section", "schemaTo": "public", "columnsFrom": ["class_section_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "enrollment_academic_session_id_fkey": {"name": "enrollment_academic_session_id_fkey", "tableFrom": "enrollment", "tableTo": "academic_session", "schemaTo": "public", "columnsFrom": ["academic_session_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_student_session_per_enrollment": {"columns": ["student_id", "academic_session_id"], "nullsNotDistinct": false, "name": "unique_student_session_per_enrollment"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.section_subject": {"name": "section_subject", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "class_section_id": {"name": "class_section_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subject_id": {"name": "subject_id", "type": "uuid", "primaryKey": false, "notNull": true}, "academic_session_id": {"name": "academic_session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subject_teacher_id": {"name": "subject_teacher_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_academic_session_id": {"name": "idx_academic_session_id", "columns": [{"expression": "academic_session_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"section_subject_class_section_id_fkey": {"name": "section_subject_class_section_id_fkey", "tableFrom": "section_subject", "tableTo": "class_section", "schemaTo": "public", "columnsFrom": ["class_section_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "section_subject_subject_id_fkey": {"name": "section_subject_subject_id_fkey", "tableFrom": "section_subject", "tableTo": "subject", "schemaTo": "public", "columnsFrom": ["subject_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "section_subject_academic_session_id_fkey": {"name": "section_subject_academic_session_id_fkey", "tableFrom": "section_subject", "tableTo": "academic_session", "schemaTo": "public", "columnsFrom": ["academic_session_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}, "section_subject_subject_teacher_id_fkey": {"name": "section_subject_subject_teacher_id_fkey", "tableFrom": "section_subject", "tableTo": "staff", "schemaTo": "public", "columnsFrom": ["subject_teacher_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_subject_per_class_section_per_session": {"columns": ["class_section_id", "subject_id", "academic_session_id"], "nullsNotDistinct": false, "name": "unique_subject_per_class_section_per_session"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}}, "enums": {"public.enrollment_status": {"name": "enrollment_status", "values": ["ACTIVE", "EXPELLED", "GRADUATED", "DECEASED", "COMPLETED", "WITHDRAWN"], "schema": "public"}, "public.enrollment_type": {"name": "enrollment_type", "values": ["ADMISSION", "TRANSFER_IN", "REPEATING", "PROMOTION"], "schema": "public"}, "public.gender": {"name": "gender", "values": ["MALE", "FEMALE", "OTHER"], "schema": "public"}, "public.guardian_relation": {"name": "guardian_relation", "values": ["FATHER", "MOTHER", "GUARDIAN"], "schema": "public"}, "public.religion": {"name": "religion", "values": ["ISLAM", "CHRISTIANITY", "HINDUISM", "BUDDHISM", "SIKHISM", "JUDAISM", "OTHER"], "schema": "public"}, "public.role_name": {"name": "role_name", "values": ["BRANCH_ADMIN", "ACCOUNTANT", "PLATFORM_ADMIN", "INSTITUTE_OWNER", "TEACHER", "SUPPORT_STAFF", "STUDENT", "GUARDIAN"], "schema": "public"}, "public.staff_department": {"name": "staff_department", "values": ["ACADEMIC", "ADMINISTRATION", "SUPPORT"], "schema": "public"}, "public.staff_type": {"name": "staff_type", "values": ["TEACHER", "SUPPORT_STAFF", "BRANCH_ADMIN", "ACCOUNTANT"], "schema": "public"}, "public.subject_type": {"name": "subject_type", "values": ["THEORY", "PRACTICAL"], "schema": "public"}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {"subscription_plan": {"columns": {"features": {"isArray": true, "dimensions": 1, "rawType": "text"}}}}}}