-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE TYPE "public"."enrollment_status" AS ENUM('ACTIVE', 'EXPELLED', 'GRADUATED', 'DECEASED', 'COMPLETED', 'WITHDRAWN');--> statement-breakpoint
CREATE TYPE "public"."enrollment_type" AS ENUM('ADMISSION', 'TRANSFER_IN', 'REPEATING', 'PROMOTION');--> statement-breakpoint
CREATE TYPE "public"."gender" AS ENUM('MALE', 'FEMALE', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."guardian_relation" AS ENUM('FATHER', 'MOTHER', '<PERSON><PERSON><PERSON><PERSON><PERSON>');--> statement-breakpoint
CREATE TYPE "public"."religion" AS ENUM('ISLAM', 'CHRISTIANITY', 'HINDUISM', 'BUDDHISM', 'SIKHISM', 'JUDAISM', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."role_name" AS ENUM('BRANCH_ADMIN', 'ACCOUNTANT', 'PLATFORM_ADMIN', 'INSTITUTE_OWNER', 'TEACHER', 'SUPPORT_STAFF', 'STUDENT', 'GUARDIAN');--> statement-breakpoint
CREATE TYPE "public"."staff_department" AS ENUM('ACADEMIC', 'ADMINISTRATION', 'SUPPORT');--> statement-breakpoint
CREATE TYPE "public"."staff_type" AS ENUM('TEACHER', 'SUPPORT_STAFF', 'BRANCH_ADMIN', 'ACCOUNTANT');--> statement-breakpoint
CREATE TYPE "public"."subject_type" AS ENUM('THEORY', 'PRACTICAL');--> statement-breakpoint
CREATE TABLE "kysely_migration" (
	"name" varchar(255) PRIMARY KEY NOT NULL,
	"timestamp" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "kysely_migration_lock" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"is_locked" integer DEFAULT 0 NOT NULL
);
--> statement-breakpoint
CREATE TABLE "role" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" "role_name" NOT NULL,
	"code" integer NOT NULL,
	"description" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "role_name_key" UNIQUE("name"),
	CONSTRAINT "role_code_key" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"phone" text NOT NULL,
	"address" text NOT NULL,
	"gender" "gender" NOT NULL,
	"photo" text,
	"role_id" uuid NOT NULL,
	"cnic" text NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"password" text NOT NULL,
	"is_password_temporary" boolean DEFAULT false NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "users_email_key" UNIQUE("email"),
	CONSTRAINT "users_phone_key" UNIQUE("phone"),
	CONSTRAINT "users_cnic_key" UNIQUE("cnic")
);
--> statement-breakpoint
CREATE TABLE "test" (
	"name" text NOT NULL,
	"date" date NOT NULL
);
--> statement-breakpoint
CREATE TABLE "institute_owner" (
	"user_id" uuid PRIMARY KEY NOT NULL,
	"has_completed_setup" boolean DEFAULT false NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "institute" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"logo" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"is_basic_setup_complete" boolean DEFAULT false NOT NULL,
	"owner_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "institute_name_key" UNIQUE("name"),
	CONSTRAINT "institute_email_key" UNIQUE("email"),
	CONSTRAINT "institute_owner_id_key" UNIQUE("owner_id")
);
--> statement-breakpoint
CREATE TABLE "branch" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" text NOT NULL,
	"name" text NOT NULL,
	"address" text NOT NULL,
	"phone" text NOT NULL,
	"is_main" boolean DEFAULT false NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"institute_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "branch_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "platform_admin" (
	"user_id" uuid PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "subscription_plan" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"title" text NOT NULL,
	"description" text,
	"price" numeric(10, 2) NOT NULL,
	"setup_charges" numeric(10, 2) NOT NULL,
	"branches" integer NOT NULL,
	"students" integer NOT NULL,
	"features" text[] NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "subscription_plan_title_key" UNIQUE("title"),
	CONSTRAINT "subscription_plan_price_key" UNIQUE("price")
);
--> statement-breakpoint
CREATE TABLE "subscription" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"plan_id" uuid NOT NULL,
	"owner_id" uuid NOT NULL,
	"status" varchar(20) NOT NULL,
	"start_date" timestamp with time zone DEFAULT now() NOT NULL,
	"payment_cycle" varchar(20) NOT NULL,
	"grace_period_days" integer DEFAULT 5 NOT NULL,
	"end_date" timestamp with time zone NOT NULL,
	"next_payment_date" timestamp with time zone NOT NULL,
	"last_payment_date" timestamp with time zone DEFAULT now() NOT NULL,
	"cancellation_date" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "subscription_owner_id_key" UNIQUE("owner_id"),
	CONSTRAINT "subscription_status_check" CHECK ((status)::text = ANY ((ARRAY['active'::character varying, 'cancelled'::character varying, 'unpaid'::character varying, 'expired'::character varying])::text[])),
	CONSTRAINT "subscription_payment_cycle_check" CHECK ((payment_cycle)::text = ANY ((ARRAY['monthly'::character varying, 'yearly'::character varying])::text[]))
);
--> statement-breakpoint
CREATE TABLE "academic_session" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"start_date" date NOT NULL,
	"end_date" date NOT NULL,
	"branch_id" uuid NOT NULL,
	"is_active" boolean DEFAULT false NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "academic_session_date_unique" UNIQUE("start_date","end_date"),
	CONSTRAINT "start_date_before_end_date" CHECK (start_date < end_date)
);
--> statement-breakpoint
CREATE TABLE "diary" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"class_section_id" uuid NOT NULL,
	"subject_id" uuid NOT NULL,
	"content" text NOT NULL,
	"date" date NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "unique_diary_entry" UNIQUE("class_section_id","subject_id","date")
);
--> statement-breakpoint
CREATE TABLE "staff" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid,
	"branch_id" uuid NOT NULL,
	"support_staff_profile_id" uuid,
	"designation" text NOT NULL,
	"department" "staff_department" NOT NULL,
	"salary" numeric(10, 2) NOT NULL,
	"type" "staff_type" NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "staff_user_id_key" UNIQUE("user_id"),
	CONSTRAINT "staff_support_staff_profile_id_key" UNIQUE("support_staff_profile_id"),
	CONSTRAINT "staff_is_either_user_or_support_staff" CHECK (((user_id IS NOT NULL) AND (support_staff_profile_id IS NULL)) OR ((user_id IS NULL) AND (support_staff_profile_id IS NOT NULL)))
);
--> statement-breakpoint
CREATE TABLE "support_staff_profile" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"phone" text NOT NULL,
	"address" text NOT NULL,
	"gender" "gender" NOT NULL,
	"photo" text,
	"cnic" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT '2025-05-29 15:17:29.29808+05' NOT NULL,
	CONSTRAINT "support_staff_profile_email_key" UNIQUE("email"),
	CONSTRAINT "support_staff_profile_phone_key" UNIQUE("phone"),
	CONSTRAINT "support_staff_profile_cnic_key" UNIQUE("cnic")
);
--> statement-breakpoint
CREATE TABLE "subject" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"marks" integer NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"academic_session_id" uuid NOT NULL,
	"type" "subject_type" NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "class" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"maximum_students" integer NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"academic_session_id" uuid NOT NULL,
	"fee_per_month" numeric(10, 2) NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "class_name_academic_session_id_unique" UNIQUE("name","academic_session_id")
);
--> statement-breakpoint
CREATE TABLE "class_section" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"class_id" uuid NOT NULL,
	"class_teacher_id" uuid NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "class_section_name_class_id_unique" UNIQUE("name","class_id")
);
--> statement-breakpoint
CREATE TABLE "guardian" (
	"user_id" uuid PRIMARY KEY NOT NULL,
	"relation" "guardian_relation" NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "student" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"registration_number" text,
	"email" text,
	"father_name" text NOT NULL,
	"address" text NOT NULL,
	"gender" "gender" NOT NULL,
	"photo" text,
	"religion" "religion" NOT NULL,
	"monthly_fee" numeric(10, 2) NOT NULL,
	"admission_date" date NOT NULL,
	"date_of_birth" date NOT NULL,
	"previous_school" text,
	"class_section_id" uuid NOT NULL,
	"guardian_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "student_email_key" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "enrollment" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"student_id" uuid NOT NULL,
	"class_section_id" uuid NOT NULL,
	"academic_session_id" uuid NOT NULL,
	"type" "enrollment_type" NOT NULL,
	"status" "enrollment_status" NOT NULL,
	"date" date NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "unique_student_session_per_enrollment" UNIQUE("student_id","academic_session_id")
);
--> statement-breakpoint
CREATE TABLE "section_subject" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"class_section_id" uuid NOT NULL,
	"subject_id" uuid NOT NULL,
	"academic_session_id" uuid NOT NULL,
	"subject_teacher_id" uuid NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "unique_subject_per_class_section_per_session" UNIQUE("class_section_id","subject_id","academic_session_id")
);
--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "users_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."role"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institute_owner" ADD CONSTRAINT "owner_user_id" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "institute" ADD CONSTRAINT "institute_owner_id" FOREIGN KEY ("owner_id") REFERENCES "public"."institute_owner"("user_id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "branch" ADD CONSTRAINT "branch_institute_id_fkey" FOREIGN KEY ("institute_id") REFERENCES "public"."institute"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "platform_admin" ADD CONSTRAINT "admin_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "subscription" ADD CONSTRAINT "subscription_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "public"."subscription_plan"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "subscription" ADD CONSTRAINT "subscription_user_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "public"."institute_owner"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "academic_session" ADD CONSTRAINT "academic_session_branch_id_fkey" FOREIGN KEY ("branch_id") REFERENCES "public"."branch"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "diary" ADD CONSTRAINT "diary_class_section_id_fkey" FOREIGN KEY ("class_section_id") REFERENCES "public"."class_section"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "diary" ADD CONSTRAINT "diary_subject_id_fkey" FOREIGN KEY ("subject_id") REFERENCES "public"."subject"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "staff" ADD CONSTRAINT "staff_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "staff" ADD CONSTRAINT "staff_branch_id_fkey" FOREIGN KEY ("branch_id") REFERENCES "public"."branch"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "staff" ADD CONSTRAINT "staff_support_staff_profile_id_fkey" FOREIGN KEY ("support_staff_profile_id") REFERENCES "public"."support_staff_profile"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "subject" ADD CONSTRAINT "subject_academic_session_id_fkey" FOREIGN KEY ("academic_session_id") REFERENCES "public"."academic_session"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class" ADD CONSTRAINT "class_academic_session_id_fkey" FOREIGN KEY ("academic_session_id") REFERENCES "public"."academic_session"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "class_section" ADD CONSTRAINT "class_section_class_id_fkey" FOREIGN KEY ("class_id") REFERENCES "public"."class"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "class_section" ADD CONSTRAINT "class_section_class_teacher_id_fkey" FOREIGN KEY ("class_teacher_id") REFERENCES "public"."staff"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "guardian" ADD CONSTRAINT "guardian_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "student" ADD CONSTRAINT "student_class_section_id_fkey" FOREIGN KEY ("class_section_id") REFERENCES "public"."class_section"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "student" ADD CONSTRAINT "student_guardian_id_fkey" FOREIGN KEY ("guardian_id") REFERENCES "public"."guardian"("user_id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "enrollment" ADD CONSTRAINT "enrollment_student_id_fkey" FOREIGN KEY ("student_id") REFERENCES "public"."student"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "enrollment" ADD CONSTRAINT "enrollment_class_section_id_fkey" FOREIGN KEY ("class_section_id") REFERENCES "public"."class_section"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "enrollment" ADD CONSTRAINT "enrollment_academic_session_id_fkey" FOREIGN KEY ("academic_session_id") REFERENCES "public"."academic_session"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "section_subject" ADD CONSTRAINT "section_subject_class_section_id_fkey" FOREIGN KEY ("class_section_id") REFERENCES "public"."class_section"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "section_subject" ADD CONSTRAINT "section_subject_subject_id_fkey" FOREIGN KEY ("subject_id") REFERENCES "public"."subject"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "section_subject" ADD CONSTRAINT "section_subject_academic_session_id_fkey" FOREIGN KEY ("academic_session_id") REFERENCES "public"."academic_session"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "section_subject" ADD CONSTRAINT "section_subject_subject_teacher_id_fkey" FOREIGN KEY ("subject_teacher_id") REFERENCES "public"."staff"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
CREATE UNIQUE INDEX "branch_is_main_unique_index" ON "branch" USING btree ("is_main" bool_ops,"institute_id" bool_ops) WHERE (is_main = true);--> statement-breakpoint
CREATE UNIQUE INDEX "academic_session_is_active_index" ON "academic_session" USING btree ("branch_id" bool_ops,"is_active" bool_ops) WHERE (is_active = true);--> statement-breakpoint
CREATE UNIQUE INDEX "staff_support_staff_profile_id_unique_index" ON "staff" USING btree ("support_staff_profile_id" uuid_ops,"branch_id" uuid_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "staff_user_id_unique_index" ON "staff" USING btree ("user_id" uuid_ops,"branch_id" uuid_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "idx_subject_name_session_id_type" ON "subject" USING btree ("name" enum_ops,"academic_session_id" enum_ops,"type" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_subject_session_id" ON "subject" USING btree ("academic_session_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_class_section_class_id" ON "class_section" USING btree ("class_id" uuid_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "idx_unique_academic_session_status" ON "enrollment" USING btree ("academic_session_id" uuid_ops,"status" enum_ops) WHERE (status = 'ACTIVE'::enrollment_status);--> statement-breakpoint
CREATE UNIQUE INDEX "idx_unique_student_per_section_if_not_repeating" ON "enrollment" USING btree ("student_id" enum_ops,"class_section_id" uuid_ops,"type" enum_ops) WHERE (type <> 'REPEATING'::enrollment_type);--> statement-breakpoint
CREATE INDEX "idx_academic_session_id" ON "section_subject" USING btree ("academic_session_id" uuid_ops);
*/