import postgres from "postgres";
import { drizzle } from "drizzle-orm/postgres-js";
import { databaseSchema } from "./schema.js";

const client = postgres(
  "postgres://totalsoft:password0011@localhost:5432/test",
);

const db = drizzle(client, {
  schema: databaseSchema,
  logger: true,
  casing: "snake_case",
});

async function main() {
  const ts = await db.query.institute.findMany({
    columns: {
      id: true,
      name: true,
      email: true,
      isActive: true,
    },
    with: { instituteOwner: true },
  });
  console.log(ts);
}

main();
